<!-- TODO: Upload image -->
<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketProfileStore } from '@stores/profile'
import avatar1 from '@images/avatars/avatar-1.png'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'

const store = useSocketStore()
const storeProfile = useSocketProfileStore()

const { user } = storeToRefs(store)
const { profile } = storeToRefs(storeProfile)

const vuetifyTheme = useTheme()

socket.on('updateProfile', data => {
  console.log({ data })

  console.log(data.data.user)

  switch (data.status) {
  case 'success':
    profile.value = data.data.profile
    user.value = data.data.user

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('uploadProfileImage', data => {
  console.log({ data })

  switch (data.status) {
  case 'success':
    user.value = data.user

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    file.value = null
    isUploading.value = false
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

// Listen for progress and status
socket.on('file-upload-progress', progress => {
  uploadProgress.value = progress
})

socket.on('file-upload-complete', message => {
  uploadStatus.value = message
  isUploading.value = false
})

socket.on('file-upload-error', error => {
  uploadStatus.value = `Error: ${ error }`
  isUploading.value = false
})

socket.on('uploadCoverImage', data => {
  console.log({ data })

  switch (data.status) {
  case 'success':
    user.value = data.user

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    fileCover.value = null
    isUploading.value = false
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

// Listen for progress and status
socket.on('file-upload-progress-cover', progress => {
  uploadProgressCover.value = progress
})

socket.on('file-upload-complete-cover', message => {
  uploadStatusCover.value = message
  isUploadingCover.value = false
})

socket.on('file-upload-error-cover', error => {
  uploadStatusCover.value = `Error: ${ error }`
  isUploadingCover.value = false
})

/*const isConfirmDialogOpen = ref(false)
const isAccountDeactivated = ref(false)
const validateAccountDeactivation = [ v => !!v || 'Please confirm account deactivation' ]*/

const zoneTypes = [
  'UTC',
  'GMT',
]

const baseUrl = 'https://api.qwotez.com'

const zone = ref('')

const timezonesUTC = [
  {
    'zone': 'Europe/Amsterdam',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Amsterdam (UTC+01:00)',
    'name': 'Amsterdam',
  },
  {
    'zone': 'Europe/Belgrade',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Belgrade (UTC+01:00)',
    'name': 'Belgrade',
  },
  {
    'zone': 'Europe/Berlin',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Berlin (UTC+01:00)',
    'name': 'Berlin',
  },
  {
    'zone': 'Europe/Bratislava',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Bratislava (UTC+01:00)',
    'name': 'Bratislava',
  },
  {
    'zone': 'Europe/Brussels',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Brussels (UTC+01:00)',
    'name': 'Brussels',
  },
  {
    'zone': 'Europe/Budapest',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Budapest (UTC+01:00)',
    'name': 'Budapest',
  },
  {
    'zone': 'Europe/Copenhagen',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Copenhagen (UTC+01:00)',
    'name': 'Copenhagen',
  },
  {
    'zone': 'Europe/Ljubljana',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Ljubljana (UTC+01:00)',
    'name': 'Ljubljana',
  },
  {
    'zone': 'Europe/Madrid',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Madrid (UTC+01:00)',
    'name': 'Madrid',
  },
  {
    'zone': 'Europe/Paris',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Paris (UTC+01:00)',
    'name': 'Paris',
  },
  {
    'zone': 'Europe/Prague',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Prague (UTC+01:00)',
    'name': 'Prague',
  },
  {
    'zone': 'Europe/Rome',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Rome (UTC+01:00)',
    'name': 'Rome',
  },
  {
    'zone': 'Europe/Sarajevo',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Sarajevo (UTC+01:00)',
    'name': 'Sarajevo',
  },
  {
    'zone': 'Europe/Skopje',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Skopje (UTC+01:00)',
    'name': 'Skopje',
  },
  {
    'zone': 'Europe/Stockholm',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Stockholm (UTC+01:00)',
    'name': 'Stockholm',
  },
  {
    'zone': 'Europe/Vienna',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Vienna (UTC+01:00)',
    'name': 'Vienna',
  },
  {
    'zone': 'Europe/Warsaw',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Warsaw (UTC+01:00)',
    'name': 'Warsaw',
  },
  {
    'zone': 'Europe/Zagreb',
    'utc': '(UTC+01:00)',
    'displayName': 'Europe/Zagreb (UTC+01:00)',
    'name': 'Zagreb',
  },
  {
    'zone': 'Europe/Athens',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Athens (UTC+02:00)',
    'name': 'Athens',
  },
  {
    'zone': 'Europe/Bucharest',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Bucharest (UTC+02:00)',
    'name': 'Bucharest',
  },
  {
    'zone': 'Africa/Cairo',
    'utc': '(UTC+02:00)',
    'displayName': 'Africa/Cairo (UTC+02:00)',
    'name': 'Cairo',
  },
  {
    'zone': 'Africa/Harare',
    'utc': '(UTC+02:00)',
    'displayName': 'Africa/Harare (UTC+02:00)',
    'name': 'Harere',
  },
  {
    'zone': 'Europe/Helsinki',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Helsinki (UTC+02:00)',
    'name': 'Helsinki',
  },
  {
    'zone': 'Europe/Istanbul',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Istanbul (UTC+02:00)',
    'name': 'Istanbul',
  },
  {
    'zone': 'Asia/Jerusalem',
    'utc': '(UTC+02:00)',
    'displayName': 'Asia/Jerusalem (UTC+02:00)',
    'name': 'Jerusalem',
  },
  {
    'zone': 'Europe/Kiev',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Kiev (UTC+02:00)',
    'name': 'Kiev',
  },
  {
    'zone': 'Europe/Minsk',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Minsk (UTC+02:00)',
    'name': 'Minsk',
  },
  {
    'zone': 'Europe/Riga',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Riga (UTC+02:00)',
    'name': 'Riga',
  },
  {
    'zone': 'Europe/Sofia',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Sofia (UTC+02:00)',
    'name': 'Sofia',
  },
  {
    'zone': 'Europe/Tallinn',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Tallinn (UTC+02:00)',
    'name': 'Tallinn',
  },
  {
    'zone': 'Europe/Vilnius',
    'utc': '(UTC+02:00)',
    'displayName': 'Europe/Vilnius (UTC+02:00)',
    'name': 'Vilnius',
  },
  {
    'zone': 'Asia/Baghdad',
    'utc': '(UTC+03:00)',
    'displayName': 'Asia/Baghdad (UTC+03:00)',
    'name': 'Baghdad',
  },
  {
    'zone': 'Asia/Kuwait',
    'utc': '(UTC+03:00)',
    'displayName': 'Asia/Kuwait (UTC+03:00)',
    'name': 'Kuwait',
  },
  {
    'zone': 'Africa/Nairobi',
    'utc': '(UTC+03:00)',
    'displayName': 'Africa/Nairobi (UTC+03:00)',
    'name': 'Nairobi',
  },
  {
    'zone': 'Asia/Riyadh',
    'utc': '(UTC+03:00)',
    'displayName': 'Asia/Riyadh (UTC+03:00)',
    'name': 'Riyadh',
  },
  {
    'zone': 'Asia/Tehran',
    'utc': '(UTC+03:30)',
    'displayName': 'Asia/Tehran (UTC+03:30)',
    'name': 'Tehran',
  },
  {
    'zone': 'Europe/Moscow',
    'utc': '(UTC+04:00)',
    'displayName': 'Europe/Moscow (UTC+04:00)',
    'name': 'Moscow',
  },
  {
    'zone': 'Asia/Baku',
    'utc': '(UTC+04:00)',
    'displayName': 'Asia/Baku (UTC+04:00)',
    'name': 'Baku',
  },
  {
    'zone': 'Europe/Volgograd',
    'utc': '(UTC+04:00)',
    'displayName': 'Europe/Volgograd (UTC+04:00)',
    'name': 'Volgograd',
  },
  {
    'zone': 'Asia/Muscat',
    'utc': '(UTC+04:00)',
    'displayName': 'Asia/Muscat (UTC+04:00)',
    'name': 'Muscat',
  },
  {
    'zone': 'Asia/Tbilisi',
    'utc': '(UTC+04:00)',
    'displayName': 'Asia/Tbilisi (UTC+04:00)',
    'name': 'Tbilisi',
  },
  {
    'zone': 'Asia/Yerevan',
    'utc': '(UTC+04:00)',
    'displayName': 'Asia/Yerevan (UTC+04:00)',
    'name': 'Yerevan',
  },
  {
    'zone': 'Asia/Kabul',
    'utc': '(UTC+04:30)',
    'displayName': 'Asia/Kabul (UTC+04:30)',
    'name': 'Kabul',
  },
  {
    'zone': 'Asia/Karachi',
    'utc': '(UTC+05:00)',
    'displayName': 'Asia/Karachi (UTC+05:00)',
    'name': 'Karachi',
  },
  {
    'zone': 'Asia/Tashkent',
    'utc': '(UTC+05:00)',
    'displayName': 'Asia/Tashkent (UTC+05:00)',
    'name': 'Tashkent',
  },
  {
    'zone': 'Asia/Kolkata',
    'utc': '(UTC+05:30)',
    'displayName': 'Asia/Kolkata (UTC+05:30)',
    'name': 'Kolkata',
  },
  {
    'zone': 'Asia/Kathmandu',
    'utc': '(UTC+05:45)',
    'displayName': 'Asia/Kathmandu (UTC+05:45)',
    'name': 'Kathmandu',
  },
  {
    'zone': 'Asia/Yekaterinburg',
    'utc': '(UTC+06:00)',
    'displayName': 'Asia/Yekaterinburg (UTC+06:00)',
    'name': 'Yekaterinburg',
  },
  {
    'zone': 'Asia/Almaty',
    'utc': '(UTC+06:00)',
    'displayName': 'Asia/Almaty (UTC+06:00)',
    'name': 'Almaty',
  },
  {
    'zone': 'Asia/Dhaka',
    'utc': '(UTC+06:00)',
    'displayName': 'Asia/Dhaka (UTC+06:00)',
    'name': 'Dhaka',
  },
  {
    'zone': 'Asia/Novosibirsk',
    'utc': '(UTC+07:00)',
    'displayName': 'Asia/Novosibirsk (UTC+07:00)',
    'name': 'Novosibirsk',
  },
  {
    'zone': 'Asia/Bangkok',
    'utc': '(UTC+07:00)',
    'displayName': 'Asia/Bangkok (UTC+07:00)',
    'name': 'Bangkok',
  },
  {
    'zone': 'Asia/Jakarta',
    'utc': '(UTC+07:00)',
    'displayName': 'Asia/Jakarta (UTC+07:00)',
    'name': 'Jakarta',
  },
  {
    'zone': 'Asia/Krasnoyarsk',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Krasnoyarsk (UTC+08:00)',
    'name': 'Krasnoyarsk',
  },
  {
    'zone': 'Asia/Chongqing',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Chongqing (UTC+08:00)',
    'name': 'Chongqing',
  },
  {
    'zone': 'Asia/Hong_Kong',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Hong_Kong (UTC+08:00)',
    'name': 'Hong Kong',
  },
  {
    'zone': 'Asia/Kuala_Lumpur',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Kuala_Lumpur (UTC+08:00)',
    'name': 'Kuala Lumpur',
  },
  {
    'zone': 'Australia/Perth',
    'utc': '(UTC+08:00)',
    'displayName': 'Australia/Perth (UTC+08:00)',
    'name': 'Perth',
  },
  {
    'zone': 'Asia/Singapore',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Singapore (UTC+08:00)',
    'name': 'Singapore',
  },
  {
    'zone': 'Asia/Taipei',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Taipei (UTC+08:00)',
    'name': 'Taipei',
  },
  {
    'zone': 'Asia/Ulaanbaatar',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Ulaanbaatar (UTC+08:00)',
    'name': 'Ulaan Bataar',
  },
  {
    'zone': 'Asia/Urumqi',
    'utc': '(UTC+08:00)',
    'displayName': 'Asia/Urumqi (UTC+08:00)',
    'name': 'Urumqi',
  },
  {
    'zone': 'Asia/Irkutsk',
    'utc': '(UTC+09:00)',
    'displayName': 'Asia/Irkutsk (UTC+09:00)',
    'name': 'Irkutsk',
  },
  {
    'zone': 'Asia/Seoul',
    'utc': '(UTC+09:00)',
    'displayName': 'Asia/Seoul (UTC+09:00)',
    'name': 'Seoul',
  },
  {
    'zone': 'Asia/Tokyo',
    'utc': '(UTC+09:00)',
    'displayName': 'Asia/Tokyo (UTC+09:00)',
    'name': 'Tokyo',
  },
  {
    'zone': 'Australia/Adelaide',
    'utc': '(UTC+09:30)',
    'displayName': 'Australia/Adelaide (UTC+09:30)',
    'name': 'Adelaide',
  },
  {
    'zone': 'Australia/Darwin',
    'utc': '(UTC+09:30)',
    'displayName': 'Australia/Darwin (UTC+09:30)',
    'name': 'Darwin',
  },
  {
    'zone': 'Asia/Yakutsk',
    'utc': '(UTC+10:00)',
    'displayName': 'Asia/Yakutsk (UTC+10:00)',
    'name': 'Yakutsk',
  },
  {
    'zone': 'Australia/Brisbane',
    'utc': '(UTC+10:00)',
    'displayName': 'Australia/Brisbane (UTC+10:00)',
    'name': 'Brisbane',
  },
  {
    'zone': 'Australia/Canberra',
    'utc': '(UTC+10:00)',
    'displayName': 'Australia/Canberra (UTC+10:00)',
    'name': 'Canberra',
  },
  {
    'zone': 'Pacific/Guam',
    'utc': '(UTC+10:00)',
    'displayName': 'Pacific/Guam (UTC+10:00)',
    'name': 'Guam',
  },
  {
    'zone': 'Australia/Hobart',
    'utc': '(UTC+10:00)',
    'displayName': 'Australia/Hobart (UTC+10:00)',
    'name': 'Hobart',
  },
  {
    'zone': 'Australia/Melbourne',
    'utc': '(UTC+10:00)',
    'displayName': 'Australia/Melbourne (UTC+10:00)',
    'name': 'Melbourne',
  },
  {
    'zone': 'Pacific/Port_Moresby',
    'utc': '(UTC+10:00)',
    'displayName': 'Port_Moresby (UTC+10:00)',
    'name': 'Port Moresby',
  },
  {
    'zone': 'Australia/Sydney',
    'utc': '(UTC+10:00)',
    'displayName': 'Australia/Sydney (UTC+10:00)',
    'name': 'Sydney',
  },
  {
    'zone': 'Asia/Vladivostok',
    'utc': '(UTC+11:00)',
    'displayName': 'Asia/Vladivostok (UTC+11:00)',
    'name': 'Vladivostok',
  },
  {
    'zone': 'Asia/Magadan',
    'utc': '(UTC+12:00)',
    'displayName': 'Asia/Magadan (UTC+12:00)',
    'name': 'Magadan',
  },
  {
    'zone': 'Pacific/Auckland',
    'utc': '(UTC+12:00)',
    'displayName': 'Pacific/Auckland (UTC+12:00)',
    'name': 'Auckland',
  },
  {
    'zone': 'Pacific/Fiji',
    'utc': '(UTC+12:00)',
    'displayName': 'Pacific/Fiji (UTC+12:00)',
    'name': 'Fiji',
  },
]

const timezonesGMT = [
  {
    'zone': 'Pacific/Midway',
    'utc': '(GMT-11:00)',
    'displayName': 'Pacific/Midway (GMT-11:00)',
    'name': 'Midway Island',
  },
  {
    'zone': 'US/Samoa',
    'utc': '(GMT-11:00)',
    'displayName': 'US/Samoa (GMT-11:00)',
    'name': 'Samoa',
  },
  {
    'zone': 'US/Hawaii',
    'utc': '(GMT-10:00)',
    'displayName': 'US/Hawaii (GMT-10:00)',
    'name': 'Hawaii',
  },
  {
    'zone': 'US/Alaska',
    'utc': '(GMT-09:00)',
    'displayName': 'US/Alaska (GMT-09:00)',
    'name': 'Alaska',
  },
  {
    'zone': 'US/Pacific',
    'utc': '(GMT-08:00)',
    'displayName': 'US/Pacific (GMT-08:00)',
    'name': 'Pacific Time (US &amp; Canada)',
  },
  {
    'zone': 'America/Tijuana',
    'utc': '(GMT-08:00)',
    'displayName': 'America/Tijuana (GMT-08:00)',
    'name': 'Tijuana',
  },
  {
    'zone': 'US/Arizona',
    'utc': '(GMT-07:00)',
    'displayName': 'US/Arizona (GMT-07:00)',
    'name': 'Arizona',
  },
  {
    'zone': 'US/Mountain',
    'utc': '(GMT-07:00)',
    'displayName': 'US/Mountain (GMT-07:00)',
    'name': 'Mountain Time (US &amp; Canada)',
  },
  {
    'zone': 'America/Chihuahua',
    'utc': '(GMT-07:00)',
    'displayName': 'America/Chihuahua (GMT-07:00)',
    'name': 'Chihuahua',
  },
  {
    'zone': 'America/Mazatlan',
    'utc': '(GMT-07:00)',
    'displayName': 'America/Mazatlan (GMT-07:00)',
    'name': 'Mazatlan',
  },
  {
    'zone': 'America/Mexico_City',
    'utc': '(GMT-06:00)',
    'displayName': 'America/Mexico (GMT-06:00)',
    'name': 'Mexico City',
  },
  {
    'zone': 'America/Monterrey',
    'utc': '(GMT-06:00)',
    'displayName': 'America/Monterrey (GMT-06:00)',
    'name': 'Monterrey',
  },
  {
    'zone': 'Canada/Saskatchewan',
    'utc': '(GMT-06:00)',
    'displayName': 'Canada/Saskatchewan (GMT-06:00)',
    'name': 'Saskatchewan',
  },
  {
    'zone': 'US/Central',
    'utc': '(GMT-06:00)',
    'displayName': 'US/Central (GMT-06:00)',
    'name': 'Central Time (US &amp; Canada)',
  },
  {
    'zone': 'US/Eastern',
    'utc': '(GMT-05:00)',
    'displayName': 'US/Eastern (GMT-05:00)',
    'name': 'Eastern Time (US &amp; Canada)',
  },
  {
    'zone': 'US/East-Indiana',
    'utc': '(GMT-05:00)',
    'displayName': 'US/East Indiana (GMT-05:00)',
    'name': 'Indiana (East)',
  },
  {
    'zone': 'America/Bogota',
    'utc': '(GMT-05:00)',
    'displayName': 'America/Bogota (GMT-05:00)',
    'name': 'Bogota',
  },
  {
    'zone': 'America/Lima',
    'utc': '(GMT-05:00)',
    'displayName': 'America/Lima (GMT-05:00)',
    'name': 'Lima',
  },
  {
    'zone': 'America/Caracas',
    'utc': '(GMT-04:30)',
    'displayName': 'America/Caracas (GMT-04:00)',
    'name': 'Caracas',
  },
  {
    'zone': 'Canada/Atlantic',
    'utc': '(GMT-04:00)',
    'displayName': 'Canada/Atlantic (GMT-04:00)',
    'name': 'Atlantic Time (Canada)',
  },
  {
    'zone': 'America/La_Paz',
    'utc': '(GMT-04:00)',
    'displayName': 'America/La_Paz (GMT-04:00)',
    'name': 'La_Paz',
  },
  {
    'zone': 'America/Santiago',
    'utc': '(GMT-04:00)',
    'displayName': 'America/Santiago (GMT-04:00)',
    'name': 'Santiago',
  },
  {
    'zone': 'Canada/Newfoundland',
    'utc': '(GMT-03:30)',
    'displayName': 'Canada/Newfoundland (GMT-03:00)',
    'name': 'Newfoundland',
  },
  {
    'zone': 'America/Buenos_Aires',
    'utc': '(GMT-03:00)',
    'displayName': 'America/Buenos Aires (GMT-03:00)',
    'name': 'Buenos Aires',
  },
  {
    'zone': 'Greenland',
    'utc': '(GMT-03:00)',
    'displayName': 'Greenland (GMT-03:00)',
    'name': 'Greenland',
  },
  {
    'zone': 'Atlantic/Stanley',
    'utc': '(GMT-02:00)',
    'displayName': 'Atlantic/Stanley (GMT-02:00)',
    'name': 'Stanley',
  },
  {
    'zone': 'Atlantic/Azores',
    'utc': '(GMT-01:00)',
    'displayName': 'Atlantic/Azores (GMT-02:00)',
    'name': 'Azores',
  },
  {
    'zone': 'Atlantic/Cape_Verde',
    'utc': '(GMT-01:00)',
    'displayName': 'Atlantic/Cape Verde (GMT-02:00)',
    'name': 'Cape Verde Is.',
  },
  {
    'zone': 'Africa/Casablanca',
    'utc': '(GMT)',
    'displayName': 'Africa/Casablanca (GMT-02:00)',
    'name': 'Casablanca',
  },
  {
    'zone': 'Europe/Dublin',
    'utc': '(GMT)',
    'displayName': 'Europe/Dublin (GMT-02:00)',
    'name': 'Dublin',
  },
  {
    'zone': 'Europe/Lisbon',
    'utc': '(GMT)',
    'displayName': 'Europe/Lisbon (GMT-02:00)',
    'name': 'Libson',
  },
  {
    'zone': 'Europe/London',
    'utc': '(GMT)',
    'displayName': 'Europe/London (GMT-02:00)',
    'name': 'London',
  },
  {
    'zone': 'Africa/Monrovia',
    'utc': '(GMT)',
    'displayName': 'Africa/Monrovia (GMT-02:00)',
    'name': 'Monrovia',
  },
]

const currencies = [
  'USD',
  'EUR',
  'GBP',
  'AUD',
  'BRL',
  'CAD',
  'CNY',
  'CZK',
  'DKK',
  'HKD',
  'HUF',
  'INR',
  'ZAR',
]

const file = ref(null)
const isUploading = ref(false)
const uploadProgress = ref(null)
const uploadStatus = ref('')
const fileCover = ref(null)
const isUploadingCover = ref(false)
const uploadProgressCover = ref(null)
const uploadStatusCover = ref('')

const handleFileChange = event => {
  const selectedFile = event.target.files[0]
  if (selectedFile) {
    file.value = selectedFile
  }
}

const uploadFile = () => {
  console.log({ file })
  if (!file.value) return

  isUploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = ''

  const fileName = file.value.name

  const reader = new FileReader()

  reader.onload = () => {
    const fileData = reader.result

    console.log(fileName)

    // Send the file via Socket.IO
    storeProfile.uploadProfileImage({
      id: user.value._id,
      profileId: user.value.profile._id,
      fileName: fileName,
      profileImg: fileData,
    })
  }

  reader.readAsDataURL(file.value)
}

const handleFileChangeCover = event => {
  console.log({ event })

  const selectedFile = event.target.files[0]
  if (selectedFile) {
    fileCover.value = selectedFile
  }
}

const uploadFileCover = () => {
  console.log({ fileCover })
  if (!fileCover.value) return

  isUploadingCover.value = true
  uploadProgressCover.value = 0
  uploadStatusCover.value = ''

  const fileName = fileCover.value.name

  const reader = new FileReader()

  reader.onload = () => {
    const fileData = reader.result

    console.log(fileName)

    // Send the file via Socket.IO
    storeProfile.uploadCoverImage({
      id: user.value._id,
      profileId: user.value.profile._id,
      fileName: fileName,
      coverImg: fileData,
    })
  }

  reader.readAsDataURL(fileCover.value)
}

const updateProfile = () => {
  storeProfile.updateProfile({
    id: user.value._id,
    profileId: user.value.profile._id,
    profile: user.value.profile,
  })
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard>
        <VCardText>
          <VRow>
            <VCol cols="6">
              <div class="d-flex mb-10">
                <!-- 👉 Avatar -->
                <VAvatar
                  v-if="user.profile && user.profile.profileImg"
                  rounded
                  size="100"
                  class="me-6"
                  :image="baseUrl + user.profile.profileImgUrl + '/' + user.profile.profileImg"
                />
                <VAvatar
                  v-else
                  rounded
                  size="100"
                  class="me-6"
                  :image="avatar1"
                />
                <!-- 👉 Upload Photo -->
                <form class="d-flex flex-column justify-center gap-4">
                  <div class="d-flex flex-wrap gap-4">
                    <VFileInput
                      ref="file"
                      label="Upload Profile Photo"
                      @change="handleFileChange"
                    />
                    <VBtn
                      class="mt-1"
                      :disabled="isUploading"
                      @click="uploadFile"
                    >
                      Upload
                    </VBtn>
                  </div>
                  <div v-if="isUploading">
                    Uploading...
                  </div>
                  <div v-if="uploadProgress !== null">
                    <VProgressLinear
                      v-model="uploadProgress"
                      height="20"
                    >
                      <span>{{ Math.ceil(uploadProgress) }}%</span>
                    </VProgressLinear>
                  </div>
                  <div v-if="uploadStatus">
                    {{ uploadStatus }}
                  </div>
                  <p class="text-body-1 mb-0">
                    Allowed JPG, GIF or PNG. Max size of 800K
                  </p>
                </form>
              </div>
            </VCol>
            <VCol cols="6">
              <div class="d-flex mb-10">
                <!-- 👉 Avatar -->
                <VAvatar
                  v-if="user.profile && user.profile.coverImg"
                  rounded
                  size="100"
                  class="me-6"
                  :image="baseUrl + user.profile.coverImgUrl + '/' + user.profile.coverImg"
                />
                <VAvatar
                  v-else
                  rounded
                  size="100"
                  class="me-6"
                  :image="avatar1"
                />
                <!-- 👉 Upload Cover Photo -->
                <form
                  id="cover"
                  class="d-flex flex-column justify-center gap-4"
                >
                  <div class="d-flex flex-wrap gap-4">
                    <VFileInput
                      ref="fileCover"
                      label="Upload Cover Photo"
                      @change="handleFileChangeCover"
                    />
                    <VBtn
                      class="mt-1"
                      :disabled="isUploadingCover"
                      @click="uploadFileCover"
                    >
                      Upload
                    </VBtn>
                  </div>
                  <div v-if="isUploadingCover">
                    Uploading...
                  </div>
                  <div v-if="uploadProgressCover !== null">
                    <VProgressLinear
                      v-model="uploadProgressCover"
                      height="20"
                    >
                      <span>{{ Math.ceil(uploadProgressCover) }}%</span>
                    </VProgressLinear>
                  </div>
                  <div v-if="uploadStatusCover">
                    {{ uploadStatusCover }}
                  </div>
                  <p class="text-body-1 mb-0">
                    Allowed JPG, GIF or PNG. Max size of 800K
                  </p>
                </form>
              </div>
            </VCol>
          </VRow>


          <!-- 👉 Form -->
          <VForm>
            <VRow>
              <!-- 👉 First Name -->
              <VCol
                md="6"
                cols="12"
              >
                <VTextField
                  v-model="user.profile.fullName"
                  placeholder="John"
                  label="Full Name"
                />
              </VCol>

              <!-- 👉 Email -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="user.profile.email"
                  label="E-mail"
                  placeholder="<EMAIL>"
                  type="email"
                />
              </VCol>

              <!-- 👉 Organization -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="user.profile.designation"
                  label="Designation"
                  placeholder="Digital Marketer"
                />
              </VCol>

              <!-- 👉 Address -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="user.profile.location"
                  label="Location"
                  placeholder="New York, NY, USA"
                />
              </VCol>

              <!-- 👉 Language -->
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="user.profile.language"
                  label="Language"
                  multiple
                  chips
                  closable-chips
                  placeholder="Select Language"
                  :items="['Afrikaans', 'Arabic', 'English', 'Hebrew', 'Hindi', 'Japanese', 'Russian', 'Spanish', 'Urdu']"
                />
              </VCol>

              <!-- 👉 Currency -->
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="user.profile.currency"
                  label="Currency"
                  placeholder="Select Currency"
                  :items="currencies"
                  :menu-props="{ maxHeight: 200 }"
                />
              </VCol>

              <VCol
                v-if="user.profile.timezone"
                cols="12"
              >
                <span class="text-primary"><b>Current Time Zone : {{ user.profile.timezone.displayName }}</b></span>
              </VCol>

              <!-- 👉 Timezone -->
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="zone"
                  label="Select Timezone Type"
                  placeholder="Select Timezone Type"
                  :items="zoneTypes"
                  :menu-props="{ maxHeight: 200 }"
                  return-object
                />
              </VCol>
              <VCol
                v-if="zone === 'UTC'"
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="user.profile.timezone"
                  label="Timezone"
                  placeholder="Select Timezone"
                  :items="timezonesUTC"
                  item-title="displayName"
                  :menu-props="{ maxHeight: 200 }"
                  return-object
                />
              </VCol>
              <VCol
                v-else-if="zone === 'GMT'"
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="user.profile.timezone"
                  label="Timezone"
                  placeholder="Select Timezone"
                  :items="timezonesGMT"
                  item-title="displayName"
                  :menu-props="{ maxHeight: 200 }"
                  return-object
                />
              </VCol>

              <!-- 👉 Form Actions -->
              <VCol
                cols="12"
                class="d-flex flex-wrap gap-4"
              >
                <VBtn @click="updateProfile">
                  Save changes
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>

    <!--
      <VCol cols="12">
      &lt;!&ndash; 👉 Delete Account &ndash;&gt;
      <VCard>
      <VCardItem class="pb-6">
      <VCardTitle>
      Delete Account
      </VCardTitle>
      </VCardItem>
      <VCardText>
      &lt;!&ndash; 👉 Checkbox and Button  &ndash;&gt;
      <div>
      <VCheckbox
      v-model="isAccountDeactivated"
      :rules="validateAccountDeactivation"
      label="I confirm my account deactivation"
      />
      </div>

      <VBtn
      :disabled="!isAccountDeactivated"
      color="error"
      class="mt-6"
      @click="isConfirmDialogOpen = true"
      >
      Deactivate Account
      </VBtn>
      </VCardText>
      </VCard>
      </VCol>
    -->
  </VRow>

  <!-- Confirm Dialog -->
<!--
  <ConfirmDialog
  v-model:is-dialog-visible="isConfirmDialogOpen"
  confirmation-question="Are you sure you want to deactivate your account?"
  confirm-title="Deactivated!"
  confirm-msg="Your account has been deactivated successfully."
  cancel-title="Cancelled"
  cancel-msg="Account Deactivation Cancelled!"
  />
-->
</template>
