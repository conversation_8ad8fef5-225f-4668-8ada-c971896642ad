<script setup>
import BillingHistoryTable from './BillingHistoryTable.vue'
import mastercard from '@images/icons/payments/mastercard.png'
import visa from '@images/icons/payments/visa.png'
import { useSubscriptionSocketStore } from '@stores/subscription'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'

const subscriptionStore = useSubscriptionSocketStore()
const authStore = useSocketStore()
const vuetifyTheme = useTheme()

const { userSubscriptionDetails } = storeToRefs(subscriptionStore)
const { user } = storeToRefs(authStore)

const selectedPaymentMethod = ref('credit-debit-atm-card')
const isPricingPlanDialogVisible = ref(false)
const isConfirmDialogVisible = ref(false)
const isCardEditDialogVisible = ref(false)
const isCardDetailSaveBilling = ref(false)
const isLoading = ref(false)

const creditCards = [
  {
    name: '<PERSON> <PERSON>',
    number: '****************',
    expiry: '12/23',
    isPrimary: true,
    type: 'visa',
    cvv: '456',
    image: mastercard,
  },
  {
    name: 'Mildred Wagner',
    number: '****************',
    expiry: '10/27',
    isPrimary: false,
    type: 'mastercard',
    cvv: '123',
    image: visa,
  },
]

const countryList = [
  'United States',
  'Canada',
  'United Kingdom',
  'Australia',
  'New Zealand',
  'India',
  'Russia',
  'China',
  'Japan',
]

const currentCardDetails = ref()

const openEditCardDialog = cardDetails => {
  currentCardDetails.value = cardDetails
  isCardEditDialogVisible.value = true
}

const cardNumber = ref(135632156548789)
const cardName = ref('john Doe')
const cardExpiryDate = ref('05/24')
const cardCvv = ref(420)

const resetPaymentForm = () => {
  cardNumber.value = 135632156548789
  cardName.value = 'john Doe'
  cardExpiryDate.value = '05/24'
  cardCvv.value = 420
  selectedPaymentMethod.value = 'credit-debit-atm-card'
}

// Computed properties for subscription data
const currentPlan = computed(() => {
  return userSubscriptionDetails.value?.currentPlan || null
})

const subscriptionStatus = computed(() => {
  return userSubscriptionDetails.value?.subscription?.status || 'trial'
})

const subscriptionProgress = computed(() => {
  const subscription = userSubscriptionDetails.value?.subscription
  if (!subscription) return 0

  const daysRemaining = subscription.daysRemaining || 0
  const totalDays = subscription.totalDays || 14

  return Math.max(0, Math.min(100, ((totalDays - daysRemaining) / totalDays) * 100))
})

const daysRemaining = computed(() => {
  return userSubscriptionDetails.value?.subscription?.daysRemaining || 0
})

const totalDays = computed(() => {
  return userSubscriptionDetails.value?.subscription?.totalDays || 14
})

const nextBillingDate = computed(() => {
  const billingDate = userSubscriptionDetails.value?.billing?.nextBillingDate
  if (!billingDate) return null

  return new Date(billingDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
})

const billingAmount = computed(() => {
  const amount = userSubscriptionDetails.value?.billing?.amount
  const currency = userSubscriptionDetails.value?.billing?.currency || 'USD'

  if (!amount) return '$0'

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount)
})

const billingCycle = computed(() => {
  return userSubscriptionDetails.value?.billing?.billingCycle || 'monthly'
})

// Fetch subscription details on component mount
const fetchSubscriptionDetails = () => {
  if (user.value?._id) {
    console.log('Fetching subscription details for user:', user.value._id)
    isLoading.value = true
    subscriptionStore.getUserSubscriptionDetails({
      id: user.value._id,
      user: user.value._id,
    })
  } else {
    console.log('No user ID available for fetching subscription details')
  }
}

// Socket listeners
socket.on('getUserSubscriptionDetails', data => {
  console.log('Received subscription details response:', data)
  isLoading.value = false

  switch (data.status) {
  case 'success':
    console.log('Subscription details received successfully:', data.data)
    subscriptionStore.userSubscriptionDetails = data.data
    break
  case 'error':
    console.error('Error fetching subscription details:', data.message)
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  }
})

// Socket listener for subscription cancellation
socket.on('cancelUserSubscription', data => {
  console.log('Received subscription cancellation response:', data)

  switch (data.status) {
  case 'success':
    console.log('Subscription cancelled successfully:', data.data)
    toast('Subscription cancelled successfully!', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    // Refresh subscription details to show updated information
    fetchSubscriptionDetails()
    break
  case 'error':
    console.error('Error cancelling subscription:', data.message)
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  }
})

socket.on('upgradeUserSubscription', data => {
  console.error('upgradeUserSubscription:', { data })
  toast(data.message, {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

// Socket listener for subscription updates
socket.on('updateUserSubscription', data => {
  console.log('Received subscription update response:', data)

  switch (data.status) {
  case 'success':
    console.log('Subscription updated successfully:', data.data)

    const {
      newSubscription,
      paystackUpdated,
      paystackMethod,
      paystackUpdateLink,
      requiresUserAction,
      billingCycle,
    } = data.data

    let message = `Subscription updated to ${newSubscription.title}`
    if (billingCycle) {
      message += ` (${billingCycle} billing)`
    }

    // Handle different Paystack update scenarios
    if (paystackMethod === 'replacement') {
      toast(message + '. Payment subscription updated automatically.', {
        autoClose: 7000,
        theme: vuetifyTheme.global.name.value,
        type: 'success',
      })

    } else if (paystackUpdated === false) {
      toast(message + '. Note: Payment provider update may need manual verification.', {
        autoClose: 8000,
        theme: vuetifyTheme.global.name.value,
        type: 'warning',
      })

    } else {
      toast(message, {
        autoClose: 7000,
        theme: vuetifyTheme.global.name.value,
        type: 'success',
      })
    }

    // Refresh subscription details to show updated information
    fetchSubscriptionDetails()
    break
  case 'error':
    console.error('Error updating subscription:', data.message)
    toast(data.message, {
      autoClose: 7000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  }
})

// Fetch data when component mounts
onMounted(() => {
  fetchSubscriptionDetails()
})

// Watch for user changes
watch(user, newUser => {
  if (newUser?._id) {
    fetchSubscriptionDetails()
  }
}, { immediate: true })

// Handle plan upgrade completion
const handlePlanUpgraded = upgradeData => {
  console.log('Plan upgraded successfully:', upgradeData)

  // Refresh subscription details to show updated information
  fetchSubscriptionDetails()
}

// Handle subscription cancellation
const handleSubscriptionCancel = confirmed => {
  if (confirmed && user.value?._id) {
    console.log('Canceling subscription for user:', user.value._id)
    subscriptionStore.cancelUserSubscription({
      id: user.value._id,
      user: user.value._id,
    })
  }
}
</script>

<template>
  <VRow>
    <!-- 👉 Current Plan -->
    <VCol cols="12">
      <VCard>
        <VCardItem class="pb-6">
          <VCardTitle>Current Plan</VCardTitle>
          <template #append>
            <VBtn
              icon
              variant="text"
              size="small"
              :loading="isLoading"
              @click="fetchSubscriptionDetails"
            >
              <VIcon icon="ri-refresh-line" />
            </VBtn>
          </template>
        </VCardItem>
        <VCardText>
          <!-- Loading State -->
          <div
            v-if="isLoading"
            class="d-flex justify-center align-center py-8"
          >
            <VProgressCircular
              indeterminate
              color="primary"
              size="48"
            />
            <span class="ml-4">Loading subscription details...</span>
          </div>

          <VRow v-else>
            <VCol
              cols="12"
              md="6"
            >
              <div class="d-flex flex-column gap-y-6">
                <div class="d-flex flex-column gap-y-1">
                  <h6 class="text-h6">
                    Your Current Plan is {{ currentPlan?.title || 'Free Trial' }}
                  </h6>
                  <div>
                    {{ currentPlan?.description || 'A simple start for everyone' }}
                  </div>
                </div>

                <div class="d-flex flex-column gap-y-1">
                  <h6 class="text-h6">
                    {{ subscriptionStatus === 'trial' ? 'Trial ends' : 'Active until' }} {{ nextBillingDate || 'N/A' }}
                  </h6>
                  <div>
                    We will send you a notification upon {{ subscriptionStatus === 'trial' ? 'trial' : 'subscription' }} expiration
                  </div>
                </div>

                <div class="d-flex flex-column gap-y-1">
                  <div class="d-flex align-center gap-x-2">
                    <h6 class="text-h6">
                      {{ billingAmount }} Per {{ billingCycle === 'yearly' ? 'Year' : 'Month' }}
                    </h6>
                    <VChip
                      v-if="currentPlan?.isPopular"
                      variant="outlined"
                      color="primary"
                      size="small"
                    >
                      Popular
                    </VChip>
                    <VChip
                      v-if="subscriptionStatus === 'trial'"
                      variant="outlined"
                      color="warning"
                      size="small"
                    >
                      Trial
                    </VChip>
                    <VChip
                      v-if="subscriptionStatus === 'expired'"
                      variant="outlined"
                      color="error"
                      size="small"
                    >
                      Expired
                    </VChip>
                    <VChip
                      v-if="subscriptionStatus === 'active'"
                      variant="outlined"
                      color="success"
                      size="small"
                    >
                      Active
                    </VChip>
                  </div>
                  <p class="text-base mb-0">
                    {{ currentPlan?.description || 'Standard plan for small to medium businesses' }}
                  </p>
                </div>
              </div>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VAlert
                v-if="subscriptionStatus === 'trial' && daysRemaining <= 3"
                type="warning"
                variant="tonal"
                title="Trial ending soon!"
                text="Your trial is ending soon. Upgrade to continue using all features."
              />
              <VAlert
                v-else-if="subscriptionStatus === 'expired'"
                type="error"
                variant="tonal"
                title="Subscription expired!"
                text="Your subscription has expired. Please renew to continue."
              />
              <VAlert
                v-else-if="subscriptionStatus === 'active' && daysRemaining <= 7"
                type="info"
                variant="tonal"
                title="Renewal reminder"
                text="Your subscription will renew soon."
              />

              <!-- progress -->
              <div v-if="subscriptionStatus !== 'expired'">
                <h6 class="d-flex text-h6 justify-space-between mt-6 mb-1">
                  <div>{{ subscriptionStatus === 'trial' ? 'Trial Days' : 'Days' }}</div>
                  <div>{{ Math.max(0, totalDays - daysRemaining) }} of {{ totalDays }} Days</div>
                </h6>
                <VProgressLinear
                  :color="subscriptionStatus === 'trial' ? 'warning' : 'primary'"
                  rounded
                  height="6"
                  :model-value="subscriptionProgress"
                />
                <p class="text-base mt-1">
                  {{ daysRemaining }} days remaining until your {{ subscriptionStatus === 'trial' ? 'trial expires' : 'plan renews' }}
                </p>
              </div>
              <div
                v-else
                class="mt-6"
              >
                <h6 class="text-h6 text-error mb-2">
                  Subscription Expired
                </h6>
                <p class="text-base">
                  Your {{ userSubscriptionDetails?.subscription?.trialEndsAt ? 'trial' : 'subscription' }} has expired. Please upgrade to continue using premium features.
                </p>
              </div>
            </VCol>

            <VCol cols="12">
              <div class="d-flex flex-wrap gap-4">
                <VBtn
                  :color="subscriptionStatus === 'expired' ? 'primary' : 'default'"
                  :variant="subscriptionStatus === 'expired' ? 'elevated' : 'elevated'"
                  @click="isPricingPlanDialogVisible = true"
                >
                  {{ subscriptionStatus === 'expired' ? 'Renew Subscription' : 'Upgrade Plan' }}
                </VBtn>

                <VBtn
                  v-if="subscriptionStatus === 'active'"
                  color="error"
                  variant="outlined"
                  @click="isConfirmDialogVisible = true"
                >
                  Cancel Subscription
                </VBtn>
              </div>
            </VCol>
          </VRow>

          <!-- 👉 Confirm Dialog -->
          <ConfirmDialog
            v-model:is-dialog-visible="isConfirmDialogVisible"
            confirmation-question="Are you sure to cancel your subscription?"
            cancel-msg="Unsubscription Cancelled!!"
            cancel-title="Cancelled"
            confirm-msg="Your subscription cancelled successfully."
            confirm-title="Unsubscribed!"
            @confirm="handleSubscriptionCancel"
          />

          <!-- 👉 plan and pricing dialog -->
          <PricingPlanDialog
            v-model:is-dialog-visible="isPricingPlanDialogVisible"
            @plan-upgraded="handlePlanUpgraded"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 Payment Methods -->
    <VCol cols="12">
      <VCard>
        <VCardItem class="pb-6">
          <VCardTitle>Payment Methods</VCardTitle>
        </VCardItem>
        <VCardText>
          <VForm @submit.prevent="() => {}">
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VRow>
                  <!-- 👉 card type switch -->
                  <VCol cols="12">
                    <VRadioGroup
                      v-model="selectedPaymentMethod"
                      inline
                    >
                      <VRadio
                        value="credit-debit-atm-card"
                        label="Credit/Debit/ATM Card"
                        color="primary"
                      />
                      <VRadio
                        value="cod-cheque"
                        label="COD/Cheque"
                        color="primary"
                      />
                    </VRadioGroup>
                  </VCol>

                  <VCol cols="12">
                    <VRow v-show="selectedPaymentMethod === 'credit-debit-atm-card'">
                      <!-- 👉 Card Number -->
                      <VCol cols="12">
                        <VTextField
                          v-model="cardNumber"
                          label="Card Number"
                          placeholder="1234 1234 1234 1234"
                          type="number"
                        />
                      </VCol>

                      <!-- 👉 Name -->
                      <VCol
                        cols="12"
                        md="6"
                      >
                        <VTextField
                          v-model="cardName"
                          label="Name"
                          placeholder="John Doe"
                        />
                      </VCol>

                      <!-- 👉 Expiry date -->
                      <VCol
                        cols="6"
                        md="3"
                      >
                        <VTextField
                          v-model="cardExpiryDate"
                          label="Expiry Date"
                          placeholder="MM/YY"
                        />
                      </VCol>

                      <!-- 👉 Cvv code -->
                      <VCol
                        cols="6"
                        md="3"
                      >
                        <VTextField
                          v-model="cardCvv"
                          type="number"
                          label="CVV Code"
                          placeholder="123"
                        />
                      </VCol>

                      <!-- 👉 Future Billing switch -->
                      <VCol cols="12">
                        <VSwitch
                          v-model="isCardDetailSaveBilling"
                          density="compact"
                          label="Save card for future billing?"
                        />
                      </VCol>

                      <!-- 👉 Payment method action button -->
                      <VCol
                        cols="12"
                        class="d-flex flex-wrap gap-4"
                      >
                        <VBtn type="submit">
                          Save changes
                        </VBtn>
                        <VBtn
                          color="secondary"
                          variant="outlined"
                          @click="resetPaymentForm"
                        >
                          Reset
                        </VBtn>
                      </VCol>
                    </VRow>

                    <p
                      v-show="selectedPaymentMethod === 'cod-cheque'"
                      class="text-base"
                    >
                      Cash on delivery is a mode of payment where you make the payment after the goods/services are received.
                    </p>
                    <p
                      v-show="selectedPaymentMethod === 'cod-cheque'"
                      class="text-base"
                    >
                      You can pay cash or make the payment via debit/credit card directly to the delivery person.
                    </p>
                  </VCol>
                </VRow>
              </VCol>

              <!-- 👉 Saved Cards -->
              <VCol
                cols="12"
                md="6"
              >
                <h6 class="text-h6 mb-6">
                  My Cards
                </h6>

                <div class="d-flex flex-column gap-y-6">
                  <VCard
                    v-for="card in creditCards"
                    :key="card.name"
                    class="bg-var-theme-background"
                    flat
                  >
                    <VCardText class="d-flex flex-sm-row flex-column">
                      <div class="text-no-wrap">
                        <img :src="card.image">
                        <div class="d-flex align-center gap-x-4">
                          <h6 class="text-h6 my-2">
                            {{ card.name }}
                          </h6>
                          <VChip
                            v-if="card.isPrimary"
                            variant="outlined"
                            color="primary"
                            size="small"
                          >
                            Primary
                          </VChip>
                        </div>
                        <div>**** **** **** {{ card.number.substring(card.number.length - 4) }}</div>
                      </div>

                      <VSpacer />

                      <div class="d-flex flex-column text-sm-end">
                        <div class="d-flex flex-wrap gap-4 order-sm-0 order-1">
                          <VBtn
                            variant="outlined"
                            size="small"
                            @click="openEditCardDialog(card)"
                          >
                            Edit
                          </VBtn>
                          <VBtn
                            color="error"
                            variant="outlined"
                            size="small"
                          >
                            Delete
                          </VBtn>
                        </div>
                        <div class="my-4 text-body-2 order-sm-1 order-0">
                          Card expires at {{ card.expiry }}
                        </div>
                      </div>
                    </VCardText>
                  </VCard>
                </div>

                <!-- 👉 Add Edit Card Dialog -->
                <CardAddEditDialog
                  v-model:is-dialog-visible="isCardEditDialogVisible"
                  :card-details="currentCardDetails"
                />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 Billing Address -->
    <VCol cols="12">
      <VCard>
        <VCardItem class="pb-6">
          <VCardTitle>Billing Address</VCardTitle>
        </VCardItem>
        <VCardText>
          <VForm @submit.prevent="() => {}">
            <VRow>
              <!-- 👉 Company name -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  label="Company Name"
                  placeholder="Pixinvent"
                />
              </VCol>

              <!-- 👉 Billing Email -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  label="Billing Email"
                  placeholder="<EMAIL>"
                />
              </VCol>

              <!-- 👉 Tax ID -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  label="Tax ID"
                  placeholder="************"
                />
              </VCol>

              <!-- 👉 Vat Number -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  label="VAT Number"
                  placeholder="121212"
                />
              </VCol>

              <!-- 👉 Mobile -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  dirty
                  label="Phone Number"
                  type="number"
                  prefix="US (+1)"
                  placeholder="****** 456 7890"
                />
              </VCol>

              <!-- 👉 Country -->
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  label="Country"
                  :items="countryList"
                  placeholder="Select Country"
                />
              </VCol>

              <!-- 👉 Billing Address -->
              <VCol cols="12">
                <VTextField
                  label="Billing Address"
                  placeholder="1234 Main St"
                />
              </VCol>

              <!-- 👉 State -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  label="State"
                  placeholder="New York"
                />
              </VCol>

              <!-- 👉 Zip Code -->
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  label="Zip Code"
                  type="number"
                  placeholder="100006"
                />
              </VCol>

              <!-- 👉 Actions Button -->
              <VCol
                cols="12"
                class="d-flex flex-wrap gap-4"
              >
                <VBtn type="submit">
                  Save changes
                </VBtn>
                <VBtn
                  type="reset"
                  color="secondary"
                  variant="outlined"
                >
                  Reset
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 Billing History -->
    <VCol cols="12">
      <BillingHistoryTable />
    </VCol>
  </VRow>
</template>
