<script setup>
import { useSocketStore } from '@stores/auth'
import { useSubscriptionSocketStore } from '@stores/subscription'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import { VNode<PERSON>enderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import registerMultiStepIllustrationDark from '@images/pages/auth-v2-register-illustration-dark.png'
import registerMultiStepIllustrationLight from '@images/pages/auth-v2-register-illustration-bordered-dark.png'
import registerMultiStepBgDark from '@images/pages/register-multi-step-bg-dark.png'
import registerMultiStepBgLight from '@images/pages/register-multi-step-bg-light.png'
import { socket } from '@socket/socket'
import { useHead } from '@unhead/vue'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import CustomRadioSubscription from '@/pages/auth/components/CustomRadioSubscription.vue'
import RedirectLoader from '@/pages/auth/components/RedirectLoader.vue'

// TODO: turn off payment redirect and add to subscription blocker

const registerMultiStepBg = useGenerateImageVariant(registerMultiStepBgLight, registerMultiStepBgDark)

definePage({
  meta: {
    layout: 'blank',
    unauthenticatedOnly: true,
    public: true,
  },
})

useHead({
  title: 'Qwote Z | Register',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

const store = useSocketStore()
const storeSubscription = useSubscriptionSocketStore()

const { subscriptionPackages } = storeToRefs(storeSubscription)

const router = useRouter()
const vuetifyTheme = useTheme()

socket.on('register', data => {
  console.log({ data })

  switch (data.status) {
  case 'success':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    /*let yearlyMonthly = ''

    if (annualMonthlyPlanPriceToggler.value) {
      yearlyMonthly = 'yearly'
    } else {
      yearlyMonthly = 'monthly'
    }

    let priced = null

    if (yearlyMonthly === 'yearly') {
      priced = form.value.subscriptionPlan.priceYearly
    } else {
      priced = form.value.subscriptionPlan.priceMonthly
    }

    // Start 14-day trial instead of immediate payment
    storeSubscription.createPaystackSubscription({
      userId: data.data,
      email: form.value.email,
      paystackPlan: form.value.paystackPlan,
      paystackPlanType: form.value.paystackPlanType,
      planId: form.value.subscriptionPlan.id,
      planType: yearlyMonthly,
      price: priced,
      paystackCustomerCode: process.env.PAYSTACK_CLIENT_CODE,
      startTrialOnly: true, // Only start trial during registration
    })*/

    setTimeout(() => {
      router.push({ name: 'auth-verify-email' })
    }, 2000)
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

// Listen for trial started event
socket.on('trialStarted', data => {
  console.log('Trial started:', data)

  if (data.status === 'success') {
    toast(`${data.message} You can start using QwoteZ immediately!`, {
      autoClose: 7000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    // Redirect to dashboard after trial starts
    setTimeout(() => {
      router.push({ name: 'dashboard' })
    }, 2000)
  }
})

socket.on('frontEndPricing', data => {
  subscriptionPackages.value = data.data

  if (subscriptionPackages.value) {
    reformatSubs()
  }
})

socket.on('trialStarted', ({ endsAt }) => {
  console.log(`Trial started! Ends at: ${new Date(endsAt).toLocaleDateString()}`)
})

socket.on('trialError', ({ message }) => {
  console.log(`Error: ${message}`)
})

socket.on('redirectToPaystack', ({ url }) => {
  window.location.href = url // Perform the redirect
  overlay.value = false
})

onMounted( () => {
  getSubsPlans()
})

const currentStep = ref(0)
const isPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const registerMultiStepIllustration = useGenerateImageVariant(registerMultiStepIllustrationLight, registerMultiStepIllustrationDark)

const overlay = ref(false)
const annualMonthlyPlanPriceToggler = ref(true)

const items = [
  {
    title: 'Account',
    subtitle: 'Account Details',
    icon: 'tabler-file-analytics',
  },
  {
    title: 'Personal',
    subtitle: 'Enter Information',
    icon: 'tabler-user',
  },
  {
    title: 'Billing',
    subtitle: 'Payment Details',
    icon: 'tabler-credit-card',
  },
]

const form = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  websiteUrl: '',

  firstName: '',
  lastName: '',
  phone: '',
  designation: '',
  address: '',
  country: '',
  city: '',
  state: null,
  currency: '',
  language: '',

  paystackPlan: '',
  paystackPlanType: '',
  subscriptionPlan: {},
  cardNumber: '',
  cardName: '',
  expiryDate: '',
  cvv: '',
})

const localSubs = ref([])

const getSubsPlans = () => {
  storeSubscription.getFrontendSubscriptionPackages()
}

const reformatSubs = () => {
  sortedSubscriptionPackages.value.forEach(sub => {
    localSubs.value.push({
      id: sub._id,
      title: sub.title,
      subtitle: sub.subtitle,
      icon: sub.icon,
      paystackPlanCodes: sub.paystackPlanCodes,
      paystackPlanType: sub.paystackPlanType,
      description: sub.description,
      priceYearly: sub.priceYearly,
      priceMonthly: sub.priceMonthly,
    })
  })
}

const onSubmit = () => {
  if (form.value.subscriptionPlan.planType === 'yearly') {
    form.value.paystackPlan = form.value.subscriptionPlan.paystackPlanCodes.yearly

    store.register(form.value)

    // overlay.value = true

    // console.log('yearly paystack: ', form.value.paystackPlan)
  } else {
    form.value.paystackPlan = form.value.subscriptionPlan.paystackPlanCodes.monthly

    store.register(form.value)

    // overlay.value = true

    // console.log('monthly paystack: ', form.value.paystackPlan)
  }
}

const checkYearlyMonthly = () => {
  if (annualMonthlyPlanPriceToggler.value) {
    form.value.subscriptionPlan.planType = 'yearly'
  } else {
    form.value.subscriptionPlan.planType = 'monthly'
  }
}

const sortedSubscriptionPackages = computed(() => {
  return [...subscriptionPackages.value].sort((a, b) => a.priceYearly - b.priceYearly)
})

watch(annualMonthlyPlanPriceToggler, checkYearlyMonthly)
</script>

<template>
  <VOverlay
    v-model="overlay"
    class="align-center justify-center"
    persistent
  >
    <RedirectLoader />
  </VOverlay>
  <RouterLink to="/">
    <div class="auth-logo d-flex align-center gap-x-3">
      <VNodeRenderer :nodes="themeConfig.app.logo" />
      <h1 class="auth-title">
        {{ themeConfig.app.title }}
      </h1>
    </div>
  </RouterLink>

  <VRow
    no-gutters
    class="auth-wrapper"
  >
    <VCol
      md="4"
      class="d-none d-md-flex"
    >
      <!-- here your illustration -->
      <div class="d-flex justify-center align-center w-100 position-relative">
        <VImg
          :src="registerMultiStepIllustration"
          class="illustration-image flip-in-rtl"
        />

        <img
          class="bg-image position-absolute w-100 flip-in-rtl"
          :src="registerMultiStepBg"
          alt="register-multi-step-bg"
          height="340"
        >
      </div>
    </VCol>

    <VCol
      cols="12"
      md="8"
      class="auth-card-v2 d-flex align-center justify-center pa-10"
      style="background-color: rgb(var(--v-theme-surface));"
    >
      <VCard
        flat
        class="mt-12 mt-sm-10"
      >
        <AppStepper
          v-model:current-step="currentStep"
          :items="items"
          :direction="$vuetify.display.smAndUp ? 'horizontal' : 'vertical'"
          icon-size="22"
          class="stepper-icon-step-bg mb-12"
        />

        <VWindow
          v-model="currentStep"
          class="disable-tab-transition"
          style="max-inline-size: 681px;"
        >
          <VForm>
            <VWindowItem>
              <h4 class="text-h4">
                Account Information
              </h4>
              <p class="text-body-1 mb-6">
                Enter Your Account Details
              </p>

              <VRow>
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.username"
                    label="Username"
                    placeholder="Johndoe"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.email"
                    label="Email"
                    placeholder="<EMAIL>"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.password"
                    label="Password"
                    placeholder="············"
                    :type="isPasswordVisible ? 'text' : 'password'"
                    autocomplete="password"
                    :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                    @click:append-inner="isPasswordVisible = !isPasswordVisible"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.confirmPassword"
                    label="Confirm Password"
                    autocomplete="confirm-password"
                    placeholder="············"
                    :type="isConfirmPasswordVisible ? 'text' : 'password'"
                    :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                    @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="form.websiteUrl"
                    label="Website URL"
                    placeholder="https://website.com/"
                    type="url"
                  />
                </VCol>
              </VRow>
            </VWindowItem>

            <VWindowItem>
              <h4 class="text-h4">
                Personal Information
              </h4>
              <p>
                Enter Your Personal Information
              </p>

              <VRow>
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.firstName"
                    label="First Name"
                    placeholder="John"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.lastName"
                    label="Last Name"
                    placeholder="Doe"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.mobile"
                    type="number"
                    label="Mobile"
                    placeholder="****** 456 7890"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.designation"
                    label="Designation"
                    placeholder="Photographer"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="form.address"
                    label="Address"
                    placeholder="1234 Main St, New York, NY 10001, USA"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="form.country"
                    label="Country"
                    placeholder="United States"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.city"
                    label="City"
                    placeholder="New York"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppSelect
                    v-model="form.state"
                    label="State"
                    placeholder="Select State / Province"
                    :items="['New York', 'California', 'Florida', 'Washington', 'Texas']"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppSelect
                    v-model="form.currency"
                    label="Preferred Currency"
                    placeholder="USD"
                    :items="['AUD', 'CAD', 'EUR', 'USD', 'ZAR']"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="form.language"
                    label="Preferred Language"
                    placeholder="English"
                  />
                </VCol>
              </VRow>
            </VWindowItem>

            <VWindowItem>
              <h4 class="text-h4">
                Select Plan
              </h4>
              <p class="text-body-1 mb-10">
                Select plan as per your requirement
              </p>

              <VSwitch
                id="pricing-plan-toggle"
                v-model="annualMonthlyPlanPriceToggler"
              >
                <template #label>
                  <div
                    v-if="annualMonthlyPlanPriceToggler"
                    class="text-body-1"
                  >
                    Pay Annually
                  </div>
                  <div
                    v-else
                    class="text-body-1"
                  >
                    Pay Monthly
                  </div>
                </template>
              </VSwitch>

              <CustomRadioSubscription
                v-model:selected-radio="form.subscriptionPlan"
                :radio-content="localSubs"
                :grid-column="{ sm: '3', cols: '12' }"
                class="mb-10"
                @update:selected-radio="checkYearlyMonthly"
              >
                <template #default="{ item }">
                  <div class="text-center">
                    <h5 class="text-h5 mb-2">
                      {{ item.title }}
                    </h5>
                    <p class="clamp-text mb-2">
                      {{ item.description }}
                    </p>

                    <div
                      v-if="annualMonthlyPlanPriceToggler"
                      class="d-flex align-center justify-center"
                    >
                      <span class="text-primary mb-2">$</span>
                      <span class="text-h4 text-primary">
                        {{ item.priceYearly }}
                      </span>
                      <span class="mt-2">/year</span>
                    </div>

                    <div
                      v-else
                      class="d-flex align-center justify-center"
                    >
                      <span class="text-primary mb-2">$</span>
                      <span class="text-h4 text-primary">
                        {{ item.priceMonthly }}
                      </span>
                      <span class="mt-2">/month</span>
                    </div>
                  </div>
                </template>
              </CustomRadioSubscription>
            </VWindowItem>
          </VForm>
        </VWindow>

        <div class="d-flex flex-wrap justify-space-between gap-x-4 mt-6">
          <VBtn
            color="secondary"
            :disabled="currentStep === 0"
            variant="tonal"
            @click="currentStep--"
          >
            <VIcon
              icon="tabler-arrow-left"
              start
              class="flip-in-rtl"
            />
            Previous
          </VBtn>

          <VBtn
            v-if="items.length - 1 === currentStep"
            color="success"
            @click="onSubmit"
          >
            submit
          </VBtn>

          <VBtn
            v-else
            @click="currentStep++"
          >
            Next

            <VIcon
              icon="tabler-arrow-right"
              end
              class="flip-in-rtl"
            />
          </VBtn>
        </div>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";

.illustration-image {
  block-size: 550px;
  inline-size: 248px;
}

.bg-image {
  inset-block-end: 0;
}
</style>
