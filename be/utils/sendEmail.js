const nodemailer = require('nodemailer')
const hbs = require('nodemailer-express-handlebars')
const path = require('path')

const sendEmail = async options => {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_PORT == 465,
      auth: {
        // user: '<EMAIL>',
        user: process.env.SMTP_EMAIL,
        pass: process.env.SMTP_PASSWORD,
      },

      /*logger: true,   // Enable logging to console
      debug: true,    // Show debug output*/
    })

    console.log({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      email: process.env.SMTP_EMAIL,
      password: process.env.SMTP_PASSWORD.length, // just check it exists
    })

    const viewpath = path.join(__dirname, '../views/')

    const handlebarOptions = {
      viewEngine: {
        extName: '.handlebars',
        partialsDir: viewpath + 'partials',
        layoutsDir: viewpath + 'layouts',
        defaultLayout: 'main',
      },
      viewPath: viewpath + 'templates',
      extName: '.handlebars',
    }

    transporter.use('compile', hbs(handlebarOptions))

    let message = {
      attachments: [
        {
          filename: 'quotzee-icon.svg',
          path: './views/templates/images/quotzee-icon.svg',
          cid: 'gemsImg',
          contentDisposition: 'inline',
        },
        {
          filename: 'ig.png',
          path: './views/templates/images/social/ig.png',
          cid: 'qzIg',
          contentDisposition: 'inline',
        },
        {
          filename: 'fb.png',
          path: './views/templates/images/social/fb.png',
          cid: 'qzFb',
          contentDisposition: 'inline',
        },
        {
          filename: 'x.png',
          path: './views/templates/images/social/x.png',
          cid: 'qzX',
          contentDisposition: 'inline',
        },
      ],
      from: `${process.env.FROM_NAME.trim()} <${process.env.FROM_EMAIL.trim()}>`,
      to: options.email,
      subject: options.subject,
      text: options.message,
      html: options.message,
      template: options.template,
      context: options.context,
    }

    /*const sendTestEmail = async () => {
      try {
        const info = await transporter.sendMail({
          from: '<EMAIL>', // sender address
          to: '<EMAIL>',          // recipient (Mail Tester email)
          subject: 'Test Email from Nodemailer',
          text: 'Hello! This is a test email sent manually via Nodemailer.',
          html: '<p>Hello! This is a <b>test email</b> sent manually via Nodemailer.</p>',
        })

        console.log('Email sent:', info.messageId)
      } catch (error) {
        console.error('Error sending email:', error)
      }
    }

    // Call the function
    sendTestEmail()*/

    const info = await transporter.sendMail(message)

    console.log('✅ Email sent:', info.response)

  } catch (error) {
    console.error('❌ Failed to send email:', error.message)
    console.error('📦 Full error object:', error)

    // optionally rethrow or handle as needed
    throw error
  }
}

module.exports = sendEmail
