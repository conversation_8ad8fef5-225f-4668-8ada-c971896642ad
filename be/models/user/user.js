const crypto = require('crypto')
const mongoose = require('mongoose')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')

const UserSchema = new mongoose.Schema({
    username: {
        type: String,
    },
    email: {
        type: String,
        required: [true, 'Please enter email'],
        unique: true,
        match: [/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Please enter valid email'],
    },
    role: {
        type: String,
        default: 'user',
    },
    ability: {
        type: [Object],
        default: [
            {
                action: 'read',
                subject: 'standard',
            },
        ],
    },
    status: {
        type: String,
    },
    verifyEmailToken: {
        type: Boolean,
        default: false,
    },
    newUser: {
        type: Boolean,
        default: true,
    },
    isAffiliate: {
        type: Boolean,
        default: false,
    },
    accountActive: {
        type: Boolean,
        default: true,
    },
    isOtpActive: {
        type: Boolean,
        default: false,
    },
    isMfaActive: {
        type: Boolean,
        default: false,
    },
    twoFactorSecret: {
        type: String,
    },
    password: {
        type: String,
        required: [true, 'Please enter password'],
        minlength: 8,
        select: false,
    },
    privacyPolicyAccepted: {
        type: Boolean,
    },
    subscriptionPlan: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SubscriptionPackage',
        required: false,
    },
    maxIdentities: {
        type: Number,
        default: 1,
        min: [1, 'Maximum identities must be at least 1'],
        max: [999, 'Maximum identities cannot exceed 999'],
    },
    maxIdentitiesOverride: {
        type: Boolean,
        default: false,
    },
    supportAuthId: {
        type: String,
        required: false,
        unique: true,
    },
    profile: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Profile',
        required: false,
        default: () => {},
    },
    notifications: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Notification',
        required: false,
    },
    identities: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Identity',
        required: false,
    },
    expenses: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Expense',
        required: false,
    },
    expenseCategories: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'ExpenseCategory',
        required: false,
    },
    teams: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Team',
        required: false,
    },
    ownedTeams: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Team',
        required: false,
    },
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    createdAt: {
        type: Date,
        default: Date.now,
    },
})

UserSchema.pre('save', async function (next) {
    if (!this.isModified('password')) {
        next()
    }

    const salt = await bcrypt.genSalt(10)

    this.password = await bcrypt.hash(this.password, salt)
})

UserSchema.methods.getSignedJwtToken = function () {
    return jwt.sign({id: this._id}, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRE,
    })
}

UserSchema.methods.matchPassword = async function (enteredPassword) {
    return await bcrypt.compare(enteredPassword, this.password)
}

UserSchema.methods.getResetPasswordToken = function () {
    const resetToken = crypto.randomBytes(20).toString('hex')

    this.resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex')

    this.resetPasswordExpire = Date.now() + 10 * 60 * 1000

    return resetToken
}

// Method to update maxIdentities based on subscription package
UserSchema.methods.updateMaxIdentitiesFromSubscription = async function () {
    if (this.maxIdentitiesOverride) {
        // If admin has overridden, don't update from subscription
        return this.maxIdentities
    }

    if (this.subscriptionPlan) {
        const SubscriptionPackage = require('../subscription/package')
        const package = await SubscriptionPackage.findById(this.subscriptionPlan)

        if (package && package.maxIdentities) {
            this.maxIdentities = package.maxIdentities
            await this.save()
            return this.maxIdentities
        }
    }

    // Default to 1 if no subscription or package found
    this.maxIdentities = 1
    await this.save()
    return this.maxIdentities
}

// Method to set admin override for maxIdentities
UserSchema.methods.setMaxIdentitiesOverride = async function (newMaxIdentities, isOverride = true) {
    this.maxIdentities = newMaxIdentities
    this.maxIdentitiesOverride = isOverride
    await this.save()
    return this.maxIdentities
}

// Method to remove admin override and revert to subscription default
UserSchema.methods.removeMaxIdentitiesOverride = async function () {
    this.maxIdentitiesOverride = false
    return await this.updateMaxIdentitiesFromSubscription()
}

// Method to check if user can create more identities
UserSchema.methods.canCreateIdentity = function () {
    const currentIdentityCount = this.identities ? this.identities.length : 0
    return currentIdentityCount < this.maxIdentities
}

// Method to get remaining identity slots
UserSchema.methods.getRemainingIdentitySlots = function () {
    const currentIdentityCount = this.identities ? this.identities.length : 0
    return Math.max(0, this.maxIdentities - currentIdentityCount)
}

module.exports = mongoose.model('User', UserSchema)
